"""
AI Driver Module
================
Implements AI opponent behavior using a modular architecture:
Perception -> Decision -> Execution.

REFACTORED V3 (Smooth Drive):
- Unified Throttle/Brake control into a single 'drive_output' signal [-1.0, 1.0].
- Implemented 'Coasting Zone' to eliminate stop-and-go oscillation.
- Added Input Smoothing to prevent binary throttle/brake switching.
- Dynamic Emergency Braking based on speed.
"""
import pygame as pg
import math
import logging
import random
from dataclasses import dataclass, field
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum, auto
from src.game.car import Car

# --- Data Structures ---

class DifficultyLevel(Enum):
    BEGINNER = auto()
    AMATEUR = auto()
    PRO = auto()
    ELITE = auto()

@dataclass
class DifficultyParams:
    speed_factor: float      # Max speed multiplier
    lookahead_mult: float    # Lookahead distance multiplier
    error_chance: float      # Chance of making a steering mistake
    braking_margin: float    # How early to brake (higher = earlier/safer)
    aggressiveness: float    # Tendency to overtake/block
    grip_bonus: float        # Extra grip for AI to compensate for lack of fine control

    # Advanced racing parameters
    drift_skill: float       # Ability to drift on corners (0.0-1.0)
    blocking_aggressiveness: float  # How aggressively to block player (0.0-1.0)
    overtaking_intelligence: float  # Intelligence in overtaking decisions (0.0-1.0)
    speed_adaptation: float  # How well AI adapts speed to track conditions (0.0-1.0)
    prediction_depth: float  # How far ahead AI predicts player movement (0.0-1.0)
    risk_tolerance: float    # Willingness to take risky maneuvers (0.0-1.0)

@dataclass
class PerceptionData:
    """Holds all data gathered by sensors."""
    # Lidar/Raycast distances (normalized 0.0-1.0, where 1.0 is max range)
    # Rays: [Front, Front-Left, Front-Right, Side-Left, Side-Right]
    lidar_distances: List[float] = field(default_factory=lambda: [1.0] * 5)

    # Opponent awareness
    nearest_opponent_dist: float = float('inf')
    nearest_opponent_angle: float = 0.0 # Degrees relative to forward
    opponent_in_front: bool = False
    opponent_behind: bool = False
    opponent_velocity: pg.Vector2 = field(default_factory=lambda: pg.Vector2(0, 0))
    predicted_opponent_pos: Optional[pg.Vector2] = None  # Predicted position in 1 second

    # Navigation
    racing_line_target: Optional[pg.Vector2] = None
    target_speed_factor: float = 1.0 # 0.0 to 1.0 based on curvature
    next_curvature: float = 0.0
    upcoming_corner_severity: float = 0.0  # 0.0 = straight, 1.0 = sharp corner
    corner_entry_distance: float = float('inf')  # Distance to next corner

    # Self state
    current_speed: float = 0.0 # Raw velocity magnitude
    current_speed_kmh: float = 0.0 # Scaled for logic
    on_track: bool = True
    position: pg.Vector2 = field(default_factory=lambda: pg.Vector2(0, 0))
    heading: pg.Vector2 = field(default_factory=lambda: pg.Vector2(1, 0))
    lateral_slip: float = 0.0  # How much the car is sliding sideways (for drift detection)

    # Track analysis
    track_width: float = 200.0  # Estimated track width at current position
    optimal_racing_line: bool = True  # Whether AI is on optimal racing line

@dataclass
class DriveCommand:
    """Output from the Decision module."""
    steering: float = 0.0 # -1.0 to 1.0
    drive_output: float = 0.0 # -1.0 (Full Brake) to 1.0 (Full Throttle)
    drift_mode: bool = False  # Whether to engage drift mode
    drift_angle: float = 0.0  # Target drift angle for corners

# --- Helper Classes ---

class LowPassFilter:
    """
    Simple Low-Pass Filter to smooth out high-frequency noise.
    y[i] = alpha * x[i] + (1 - alpha) * y[i-1]
    """
    def __init__(self, alpha: float = 0.1):
        self.alpha = alpha
        self.value = 0.0

    def update(self, new_value: float) -> float:
        self.value = self.alpha * new_value + (1.0 - self.alpha) * self.value
        return self.value

    def reset(self, value: float = 0.0):
        self.value = value

class RacingLineFollower:
    """
    Advanced path following system using vector projection and look-ahead.
    Calculates optimal steering and speed based on a defined racing line.
    """
    def __init__(self, points: List[Tuple[float, float]], is_circuit: bool = True):
        raw_points = [pg.Vector2(p) for p in points]
        # Brute Force RDP: Epsilon 50.0 to flatten everything
        self.points: List[pg.Vector2] = self.simplify_waypoints(raw_points, epsilon=50.0)
        self.is_circuit: bool = is_circuit
        self.total_length: float = 0.0
        self.segment_lengths: List[float] = []
        self._precompute_lengths()
        self.last_index: int = 0

    def simplify_waypoints(self, points: List[pg.Vector2], epsilon: float = 15.0) -> List[pg.Vector2]:
        """
        Uses the Ramer-Douglas-Peucker algorithm to aggressively flatten the path.
        epsilon: The maximum distance a point can be from the straight line to be removed.
                 Higher epsilon (e.g., 20.0) = Straighter, blockier lines.
                 Lower epsilon (e.g., 5.0) = More detailed curve.
        """
        if len(points) < 3:
            return points

        # Helper function for perpendicular distance
        def perpendicular_distance(point, start, end):
            if start == end:
                return (point - start).length()
            
            # Vector from start to end
            line_vec = end - start
            # Vector from start to point
            point_vec = point - start
            
            line_len = line_vec.length()
            if line_len == 0: return 0
            
            # Project point_vec onto line_vec to find distance
            # Cross product in 2D gives the area of parallelogram.
            # Area / Base = Height (Distance)
            return abs(line_vec.x * point_vec.y - line_vec.y * point_vec.x) / line_len

        # --- RDP Core ---
        dmax = 0.0
        index = 0
        end = len(points) - 1
        
        for i in range(1, end):
            d = perpendicular_distance(points[i], points[0], points[end])
            if d > dmax:
                index = i
                dmax = d

        if dmax > epsilon:
            # If max distance is greater than epsilon, recursively simplify
            rec_results1 = self.simplify_waypoints(points[:index+1], epsilon)
            rec_results2 = self.simplify_waypoints(points[index:], epsilon)
            
            # Combine results (avoid duplicating the middle point)
            return rec_results1[:-1] + rec_results2
        else:
            # If all points are close to the line, just keep start and end
            return [points[0], points[end]]

    def _precompute_lengths(self) -> None:
        self.segment_lengths = []
        self.total_length = 0.0
        if not self.points:
            return

        num_segments = len(self.points) if self.is_circuit else len(self.points) - 1
        for i in range(num_segments):
            p1 = self.points[i]
            p2 = self.points[(i + 1) % len(self.points)]
            length = p1.distance_to(p2)
            self.segment_lengths.append(length)
            self.total_length += length

    def get_target(self, car_pos: pg.Vector2, lookahead_distance: float) -> Tuple[pg.Vector2, float, float]:
        """
        Returns: (target_point, speed_factor, curvature)
        """
        if not self.points:
            return car_pos, 1.0, 0.0

        # 1. Find closest point on the polyline
        closest_point, closest_idx, t = self._find_closest_point(car_pos)
        
        # 2. Walk forward along the line
        target_point, target_idx = self._get_point_at_distance(closest_idx, t, lookahead_distance)
        
        # 3. Calculate curvature (Look ahead slightly more for curvature to anticipate turns)
        curvature_lookahead_idx = (target_idx + 5) % len(self.points)
        curvature = self._calculate_curvature(curvature_lookahead_idx)
        
        # Speed factor based on curvature
        # Smoother curve: 1.0 - (curvature * 3.0) clamped to [0.3, 1.0]
        speed_factor = max(0.35, 1.0 - (curvature * 3.0))
        
        return target_point, speed_factor, curvature

    def get_continuous_target(self, car_pos: pg.Vector2, search_start_index: int, lookahead: float) -> Tuple[pg.Vector2, int]:
        """
        Finds a target point strictly AHEAD of the car along the path,
        regardless of whether we 'hit' a waypoint or not.
        Returns: (target_point, new_index)
        """
        if not self.points:
            return car_pos, search_start_index

        # 1. Find the closest point on the path segment (Projected Point)
        min_dist = float('inf')
        closest_seg_index = search_start_index
        projected_point = self.points[search_start_index]
        
        # Optimization: Only search a few segments ahead of current index
        # Handle wrapping for circuit
        num_points = len(self.points)
        search_range = 5
        
        for i in range(search_range):
            idx = (search_start_index + i) % num_points
            next_idx = (idx + 1) % num_points
            
            p1 = self.points[idx]
            p2 = self.points[next_idx]
            
            # Project car position onto line segment p1-p2
            curr_proj = self._get_closest_point_on_segment(car_pos, p1, p2)
            dist = car_pos.distance_squared_to(curr_proj)
            
            if dist < min_dist:
                min_dist = dist
                closest_seg_index = idx
                projected_point = curr_proj

        # 2. Update the stored "current index" to the closest segment
        # This ensures the index never lags behind.
        self.last_index = closest_seg_index

        # 3. Walk forward from the Projected Point by LOOKAHEAD_DISTANCE
        remaining_dist = lookahead
        
        # Start form projected point
        curr = projected_point
        idx = closest_seg_index
        
        # Safety loop limit
        loop_count = 0
        max_loops = len(self.points)
        
        while remaining_dist > 0 and loop_count < max_loops:
            next_idx = (idx + 1) % num_points
            p_next = self.points[next_idx]
            dist_to_next = curr.distance_to(p_next)
            
            if remaining_dist <= dist_to_next:
                # The target is on this segment
                direction = (p_next - curr).normalize() if dist_to_next > 0 else pg.Vector2(0,0)
                return curr + (direction * remaining_dist), self.last_index
            else:
                # Move to next segment
                remaining_dist -= dist_to_next
                curr = p_next
                idx = next_idx
                loop_count += 1
                
        return self.points[(idx + 1) % num_points], self.last_index

    def _get_closest_point_on_segment(self, p: pg.Vector2, a: pg.Vector2, b: pg.Vector2) -> pg.Vector2:
        ap = p - a
        ab = b - a
        len_sq = ab.length_squared()
        if len_sq == 0:
            return a
        t = max(0.0, min(1.0, ap.dot(ab) / len_sq))
        return a + ab * t

    def _find_closest_point(self, pos: pg.Vector2) -> Tuple[pg.Vector2, int, float]:
        min_dist_sq = float('inf')
        best_point = self.points[0]
        best_idx = 0
        best_t = 0.0

        # Optimization: Windowed search around last known index
        # Search 10 points backward and 20 forward
        num_points = len(self.points)
        num_segments = num_points if self.is_circuit else num_points - 1
        
        search_indices = range(num_segments)
        
        # Only use windowed search if we have enough points to justify it
        if num_points > 50:
            # Create a list of indices to check, handling wrapping
            start_offset = -10
            end_offset = 20
            search_indices = []
            for k in range(start_offset, end_offset):
                idx = (self.last_index + k) % num_segments
                search_indices.append(idx)

        for i in search_indices:
            p1 = self.points[i]
            p2 = self.points[(i + 1) % num_points]
            v_w = pos - p1
            v_seg = p2 - p1
            seg_len_sq = v_seg.length_squared()
            
            if seg_len_sq == 0:
                t = 0.0
            else:
                t = max(0.0, min(1.0, v_w.dot(v_seg) / seg_len_sq))
                
            proj = p1 + v_seg * t
            dist_sq = pos.distance_squared_to(proj)
            
            if dist_sq < min_dist_sq:
                min_dist_sq = dist_sq
                best_point = proj
                best_idx = i
                best_t = t

        self.last_index = best_idx
        return best_point, best_idx, best_t

    def _get_point_at_distance(self, start_idx: int, current_t: float, distance: float) -> Tuple[pg.Vector2, int]:
        remaining_dist = distance
        current_idx = start_idx
        
        # First segment
        p1 = self.points[current_idx]
        p2 = self.points[(current_idx + 1) % len(self.points)]
        seg_len = self.segment_lengths[current_idx]
        
        dist_on_seg = current_t * seg_len
        dist_left_on_seg = seg_len - dist_on_seg
        
        if remaining_dist <= dist_left_on_seg:
            new_t = (dist_on_seg + remaining_dist) / seg_len
            return p1.lerp(p2, new_t), current_idx
            
        remaining_dist -= dist_left_on_seg
        current_idx = (current_idx + 1) % len(self.points)
        
        max_steps = len(self.points) * 2
        steps = 0
        
        while steps < max_steps:
            if not self.is_circuit and current_idx >= len(self.points) - 1:
                return self.points[-1], len(self.points) - 1
                
            seg_len = self.segment_lengths[current_idx]
            
            if remaining_dist <= seg_len:
                t = remaining_dist / seg_len
                p1 = self.points[current_idx]
                p2 = self.points[(current_idx + 1) % len(self.points)]
                return p1.lerp(p2, t), current_idx
                
            remaining_dist -= seg_len
            current_idx = (current_idx + 1) % len(self.points)
            steps += 1
            
        return self.points[start_idx], start_idx

    def _calculate_curvature(self, idx: int) -> float:
        # Use a wider window to calculate curvature to reduce noise
        # But adapt to the density of points (sparse waypoints vs dense centerline)
        num_points = len(self.points)
        
        # If points are sparse (e.g. < 50), use a smaller offset (1)
        # If points are dense (e.g. > 100), use a larger offset (5)
        if num_points < 50:
            offset = 1
        else:
            offset = 5
            
        prev_idx = (idx - offset) % num_points
        next_idx = (idx + offset) % num_points
        
        p_prev = self.points[prev_idx]
        p_curr = self.points[idx]
        p_next = self.points[next_idx]
        
        v1 = (p_curr - p_prev).normalize() if (p_curr - p_prev).length_squared() > 0 else pg.Vector2(1,0)
        v2 = (p_next - p_curr).normalize() if (p_next - p_curr).length_squared() > 0 else pg.Vector2(1,0)
        
        dot = v1.dot(v2)
        # 1.0 means straight, -1.0 means 180 turn
        # Curvature: 0.0 (straight) to 1.0 (sharp turn)
        return (1.0 - dot) / 2.0

# --- Modules ---

class PerceptionModule:
    """
    Handles sensory input: Raycasting, Opponent Detection, Track Analysis.
    """
    def __init__(self, car: Car, track_manager, tmx_map=None, is_circuit: bool = True):
        self.car = car
        self.track_manager = track_manager
        self.tmx_map = tmx_map
        self.racing_line = None
        self.lookahead_mult = 1.0
        self.using_waypoints = False # Flag to indicate if we are using sparse waypoints
        
        # Initialize RacingLineFollower (Prefer RacingLine, fallback to Waypoints)
        points = []
        if hasattr(track_manager, 'racing_line') and track_manager.racing_line:
            points = track_manager.racing_line
            self.using_waypoints = False
        elif hasattr(track_manager, 'waypoints') and track_manager.waypoints:
            points = [(p.x, p.y) for p in track_manager.waypoints]
            self.using_waypoints = True
            
        if points:
            self.racing_line = RacingLineFollower(points, is_circuit)
        
        # Raycast config
        self.ray_angles = [0, -30, 30, -90, 90] # Degrees relative to heading
        self.ray_length = 300.0
        self.debug_rays = [] # List of (start, end, color)
        
        # Smoothing for lookahead
        self.smoothed_speed = 0.0

    def scan(self, opponents: List[Car], current_wp_index: int = 0) -> PerceptionData:
        data = PerceptionData()
        data.position = pg.Vector2(self.car.position)
        data.heading = pg.Vector2(1, 0).rotate(self.car.angle)
        data.current_speed = self.car.velocity.length()
        data.current_speed_kmh = data.current_speed * 15.0 # Sync with car.py units
        
        # Smooth speed for stable lookahead
        self.smoothed_speed = self.smoothed_speed * 0.9 + data.current_speed * 0.1
        
        # 1. Check Track Status (Raycasting)
        if self.tmx_map:
            data.lidar_distances = self._perform_raycast(data.position, data.heading)
            # Check if center is on track
            data.on_track = self.tmx_map.is_on_road(data.position.x, data.position.y)
        
        # 2. Enhanced Opponent Awareness
        if opponents:
            nearest = min(opponents, key=lambda o: o.position.distance_squared_to(data.position))
            dist = nearest.position.distance_to(data.position)
            data.nearest_opponent_dist = dist
            data.opponent_velocity = nearest.velocity

            # Angle to opponent
            to_opp = nearest.position - data.position
            if to_opp.length_squared() > 0:
                angle = data.heading.angle_to(to_opp)
                data.nearest_opponent_angle = angle
                # Check if in front (within 60 degree cone)
                data.opponent_in_front = abs(angle) < 30 and dist < 200
                # Check if behind (within 120 degree cone behind)
                data.opponent_behind = abs(angle) > 150 and dist < 150

            # Predict opponent position
            self._predict_opponent_movement(data, opponents)

        # 3. Navigation Target (The "Rabbit" - Smooth Lookahead)
        if self.racing_line:
            # Dynamic lookahead adjusted by difficulty
            # Base 300px + speed factor.
            # Using smoothed speed prevents the target from jittering when braking/accelerating
            lookahead = (300.0 + (self.smoothed_speed * 15.0)) * self.lookahead_mult
            
            if self.using_waypoints:
                # Use Continuous Progress Lookahead
                # We pass current_wp_index as a hint, but trust the geometric projection
                target, new_index = self.racing_line.get_continuous_target(
                    data.position, current_wp_index, lookahead
                )
                # Store the updated index to sync back to AIDriver
                data.matched_waypoint_index = new_index
                
                # Dummy values for deprecated fields
                speed_factor = 1.0
                curvature = 0.0
            else:
                # Use geometric projection for dense racing lines
                target, speed_factor, curvature = self.racing_line.get_target(data.position, lookahead)
                
            data.racing_line_target = target
            data.target_speed_factor = speed_factor
            data.next_curvature = curvature

            # 4. Advanced Track Analysis
            self._analyze_upcoming_corners(data, lookahead)
            self._calculate_lateral_slip(data)

        return data

    def _perform_raycast(self, pos: pg.Vector2, heading: pg.Vector2) -> List[float]:
        distances = []
        self.debug_rays = []
        
        step_size = 20 # px
        
        for angle in self.ray_angles:
            ray_dir = heading.rotate(angle)
            dist = 0.0
            hit = False
            
            # March ray
            curr_pos = pg.Vector2(pos)
            for _ in range(int(self.ray_length / step_size)):
                curr_pos += ray_dir * step_size
                dist += step_size
                
                if not self.tmx_map.is_on_road(curr_pos.x, curr_pos.y):
                    hit = True
                    break
            
            # Normalize distance (0.0 = collision, 1.0 = clear)
            norm_dist = dist / self.ray_length
            distances.append(norm_dist)
            
            # Debug visual
            color = (0, 255, 0) if not hit else (255, 0, 0)
            self.debug_rays.append((pos, curr_pos, color))

        return distances

    def _predict_opponent_movement(self, data: PerceptionData, opponents: List[Car]):
        """
        Predict where the opponent will be in the near future based on current velocity.
        """
        if not opponents:
            return

        nearest = min(opponents, key=lambda o: o.position.distance_squared_to(data.position))

        # Predict position 1 second ahead
        prediction_time = 1.0
        predicted_pos = nearest.position + (nearest.velocity * prediction_time * 60)  # 60 fps
        data.predicted_opponent_pos = predicted_pos

class PredictionModule:
    """
    Advanced prediction and strategic planning module for AI racing behavior.
    """
    def __init__(self, difficulty_params: DifficultyParams):
        self.params = difficulty_params
        self.prediction_history = []  # Store recent predictions for learning
        self.strategic_plan = None    # Current strategic plan
        self.plan_update_timer = 0.0

    def update_strategic_plan(self, data: PerceptionData, dt: float):
        """
        Update the AI's strategic racing plan based on current situation.
        """
        self.plan_update_timer += dt

        # Update plan every 0.5 seconds or when situation changes significantly
        if self.plan_update_timer > 0.5 or self._situation_changed(data):
            self.strategic_plan = self._create_strategic_plan(data)
            self.plan_update_timer = 0.0

    def _situation_changed(self, data: PerceptionData) -> bool:
        """
        Detect if the racing situation has changed significantly.
        """
        if not self.strategic_plan:
            return True

        # Check for significant changes in opponent position
        if hasattr(self.strategic_plan, 'opponent_distance'):
            distance_change = abs(data.nearest_opponent_dist - self.strategic_plan.opponent_distance)
            if distance_change > 50:  # 50 pixel change is significant
                return True

        # Check for track condition changes
        if hasattr(self.strategic_plan, 'corner_severity'):
            severity_change = abs(data.upcoming_corner_severity - self.strategic_plan.corner_severity)
            if severity_change > 0.2:
                return True

        return False

    def _create_strategic_plan(self, data: PerceptionData) -> Dict[str, Any]:
        """
        Create a strategic plan based on current race situation.
        """
        plan = {
            'primary_objective': self._determine_primary_objective(data),
            'speed_strategy': self._plan_speed_strategy(data),
            'positioning_strategy': self._plan_positioning_strategy(data),
            'risk_assessment': self._assess_risks(data),
            'opponent_distance': data.nearest_opponent_dist,
            'corner_severity': data.upcoming_corner_severity,
            'confidence': self.params.prediction_depth
        }

        return plan

    def _determine_primary_objective(self, data: PerceptionData) -> str:
        """
        Determine the AI's primary objective based on current situation.
        """
        # Priority order: Safety -> Position -> Speed

        if not data.on_track or data.lidar_distances[0] < 0.3:
            return "SAFETY"  # Avoid crashes

        if data.opponent_in_front and data.nearest_opponent_dist < 200:
            if self.params.aggressiveness > 0.5:
                return "OVERTAKE"  # Try to pass
            else:
                return "FOLLOW"    # Stay behind safely

        if data.opponent_behind and data.nearest_opponent_dist < 150:
            if self.params.blocking_aggressiveness > 0.3:
                return "DEFEND"    # Block opponent
            else:
                return "ESCAPE"    # Pull away

        return "OPTIMIZE"  # Focus on optimal lap time

    def _plan_speed_strategy(self, data: PerceptionData) -> Dict[str, float]:
        """
        Plan speed strategy for upcoming track sections.
        """
        strategy = {
            'current_target': 1.0,  # Speed multiplier
            'corner_approach': 1.0,
            'corner_exit': 1.0,
            'straight_line': 1.0
        }

        # Adjust based on upcoming corners
        if data.corner_entry_distance < 300:
            corner_factor = 1.0 - (data.upcoming_corner_severity * 0.4)
            strategy['corner_approach'] = corner_factor

            # Plan corner exit speed
            if self.params.drift_skill > 0.5:
                # Skilled drivers can exit corners faster
                strategy['corner_exit'] = corner_factor + 0.2
            else:
                strategy['corner_exit'] = corner_factor

        # Straight line strategy
        if data.upcoming_corner_severity < 0.2:
            strategy['straight_line'] = 1.0 + (self.params.speed_adaptation * 0.2)

        return strategy

    def _plan_positioning_strategy(self, data: PerceptionData) -> Dict[str, float]:
        """
        Plan positioning strategy relative to opponents and track.
        """
        strategy = {
            'racing_line_priority': 1.0,  # How important is staying on racing line
            'defensive_positioning': 0.0,  # How much to focus on blocking
            'overtaking_positioning': 0.0  # How much to focus on overtaking setup
        }

        if data.opponent_behind and data.nearest_opponent_dist < 200:
            strategy['defensive_positioning'] = self.params.blocking_aggressiveness
            strategy['racing_line_priority'] *= 0.8  # Less focus on optimal line

        if data.opponent_in_front and data.nearest_opponent_dist < 250:
            strategy['overtaking_positioning'] = self.params.overtaking_intelligence
            strategy['racing_line_priority'] *= 0.7  # Prepare for overtaking

        return strategy

    def _assess_risks(self, data: PerceptionData) -> Dict[str, float]:
        """
        Assess various risks in the current situation.
        """
        risks = {
            'collision_risk': 0.0,
            'off_track_risk': 0.0,
            'overtaking_risk': 0.0,
            'corner_risk': 0.0
        }

        # Collision risk
        if data.nearest_opponent_dist < 100:
            risks['collision_risk'] = 1.0 - (data.nearest_opponent_dist / 100.0)

        # Off-track risk
        if min(data.lidar_distances) < 0.4:
            risks['off_track_risk'] = 1.0 - min(data.lidar_distances)

        # Corner risk
        if data.upcoming_corner_severity > 0.5 and data.corner_entry_distance < 200:
            risks['corner_risk'] = data.upcoming_corner_severity * (1.0 - data.corner_entry_distance / 200.0)

        # Overtaking risk
        if data.opponent_in_front and data.nearest_opponent_dist < 200:
            risks['overtaking_risk'] = 0.5 + (data.upcoming_corner_severity * 0.3)

        return risks

    def get_strategic_recommendation(self, data: PerceptionData) -> Dict[str, Any]:
        """
        Get strategic recommendations based on current plan and situation.
        """
        if not self.strategic_plan:
            return {}

        recommendations = {
            'should_be_aggressive': False,
            'should_be_defensive': False,
            'should_prioritize_safety': False,
            'recommended_speed_factor': 1.0,
            'recommended_positioning': 0.0
        }

        # Apply risk tolerance
        total_risk = sum(self.strategic_plan['risk_assessment'].values())
        risk_threshold = 1.0 - self.params.risk_tolerance

        if total_risk > risk_threshold:
            recommendations['should_prioritize_safety'] = True
            recommendations['recommended_speed_factor'] = 0.8
        else:
            # Normal racing recommendations
            objective = self.strategic_plan['primary_objective']

            if objective == "OVERTAKE":
                recommendations['should_be_aggressive'] = True
                recommendations['recommended_speed_factor'] = 1.1

            elif objective == "DEFEND":
                recommendations['should_be_defensive'] = True
                recommendations['recommended_positioning'] = self._calculate_defensive_positioning(data)

            elif objective == "OPTIMIZE":
                recommendations['recommended_speed_factor'] = 1.0 + (self.params.speed_adaptation * 0.1)

        return recommendations

    def _calculate_defensive_positioning(self, data: PerceptionData) -> float:
        """
        Calculate optimal defensive positioning.
        """
        if not data.opponent_behind:
            return 0.0

        # Position to block the most likely overtaking side
        if data.nearest_opponent_angle > 0:
            return 0.3  # Block right side
        else:
            return -0.3  # Block left side





class DecisionModule:
    """
    FSM for high-level decision making.
    States: RACING, OVERTAKING, RECOVERY, DEFENDING
    """
    def __init__(self, level: DifficultyLevel):
        self.level = level
        self.state = "RACING"
        
        # Initialize Difficulty Profiles
        # Enhanced with advanced racing parameters
        self.profiles = {
            DifficultyLevel.BEGINNER: DifficultyParams(
                speed_factor=0.6, lookahead_mult=0.7, error_chance=0.15, braking_margin=1.5,
                aggressiveness=0.1, grip_bonus=1.05,
                drift_skill=0.1, blocking_aggressiveness=0.0, overtaking_intelligence=0.2,
                speed_adaptation=0.3, prediction_depth=0.1, risk_tolerance=0.1
            ),
            DifficultyLevel.AMATEUR: DifficultyParams(
                speed_factor=0.75, lookahead_mult=0.9, error_chance=0.08, braking_margin=1.4,
                aggressiveness=0.3, grip_bonus=1.1,
                drift_skill=0.3, blocking_aggressiveness=0.2, overtaking_intelligence=0.4,
                speed_adaptation=0.5, prediction_depth=0.3, risk_tolerance=0.3
            ),
            DifficultyLevel.PRO: DifficultyParams(
                speed_factor=0.9, lookahead_mult=1.4, error_chance=0.02, braking_margin=1.1,
                aggressiveness=0.7, grip_bonus=1.15,
                drift_skill=0.7, blocking_aggressiveness=0.6, overtaking_intelligence=0.7,
                speed_adaptation=0.8, prediction_depth=0.7, risk_tolerance=0.6
            ),
            DifficultyLevel.ELITE: DifficultyParams(
                speed_factor=1.0, lookahead_mult=1.6, error_chance=0.0, braking_margin=1.0,
                aggressiveness=0.9, grip_bonus=1.2,
                drift_skill=0.9, blocking_aggressiveness=0.8, overtaking_intelligence=0.9,
                speed_adaptation=1.0, prediction_depth=0.9, risk_tolerance=0.8
            )
        }
        self.params = self.profiles.get(level, self.profiles[DifficultyLevel.AMATEUR])
        
        # Steering Filter (Low Pass)
        self.steer_filter = LowPassFilter(alpha=0.15) # Low alpha = heavy smoothing
        
        # Drive Output Filter (Low Pass)
        # Reduced alpha to 0.05 to eliminate "stop and go" oscillations
        self.drive_filter = LowPassFilter(alpha=0.05)

    def decide(self, data: PerceptionData, dt: float) -> DriveCommand:
        cmd = DriveCommand()
        
        # State Transitions
        
        # Check if we are lost (too far from target waypoint)
        dist_to_target = float('inf')
        if data.racing_line_target:
             dist_to_target = data.position.distance_to(data.racing_line_target)
             
        if not data.on_track or dist_to_target > 300: # Increased margin for "Lost"
            self.state = "RECOVERY"
        elif data.opponent_in_front and data.nearest_opponent_dist < 150 and self.params.aggressiveness > 0.3:
            self.state = "OVERTAKING"
        elif not data.opponent_in_front and data.nearest_opponent_dist < 100 and self.params.aggressiveness > 0.5:
            # Check if opponent is behind (angle > 90 or < -90)
            if abs(data.nearest_opponent_angle) > 90:
                self.state = "DEFENDING"
            else:
                self.state = "RACING"
        else:
            self.state = "RACING"

        # State Logic
        if self.state == "RECOVERY":
            self._handle_recovery(data, cmd, dt)
        elif self.state == "OVERTAKING":
            self._handle_overtaking(data, cmd, dt)
        elif self.state == "DEFENDING":
            self._handle_defending(data, cmd, dt)
        else:
            self._handle_racing(data, cmd, dt)
            
        # Emergency Brake (Collision Avoidance)
        # Dynamic threshold based on speed
        # At low speed (0), threshold is 0.15. At high speed (200), threshold is 0.35
        speed_ratio = min(1.0, data.current_speed_kmh / 200.0)
        lidar_threshold = 0.15 + (speed_ratio * 0.2)
        
        if data.lidar_distances and data.lidar_distances[0] < lidar_threshold:
             # Wall imminent
             cmd.drive_output = -1.0 # Full Brake
             # Steer away from wall
             if data.lidar_distances[1] > data.lidar_distances[2]:
                 cmd.steering = -1.0
             else:
                 cmd.steering = 1.0

        # Apply Low Pass Filters
        cmd.steering = self.steer_filter.update(cmd.steering)
        cmd.drive_output = self.drive_filter.update(cmd.drive_output)

        return cmd

    def _should_drift(self, data: PerceptionData) -> bool:
        """
        Determine if AI should initiate drift based on corner analysis and skill level.
        """
        # Only drift if we have sufficient skill
        if self.params.drift_skill < 0.3:
            return False

        # Check if we're approaching a corner that benefits from drifting
        corner_severity = data.upcoming_corner_severity
        speed_ratio = data.current_speed_kmh / 100.0  # Normalize to typical racing speed

        # Drift conditions:
        # 1. Sharp corner (severity > 0.6)
        # 2. High speed entry (speed > 60 km/h)
        # 3. Close enough to corner (< 150 units)
        should_drift = (
            corner_severity > 0.6 and
            speed_ratio > 0.6 and
            data.corner_entry_distance < 150.0 and
            data.on_track
        )

        # Add skill-based randomness
        drift_probability = self.params.drift_skill * 0.8
        if random.random() > drift_probability:
            should_drift = False

        return should_drift

    def _calculate_drift_parameters(self, data: PerceptionData) -> Tuple[float, float]:
        """
        Calculate optimal drift angle and entry speed for the current corner.
        Returns: (drift_angle, target_speed_factor)
        """
        corner_severity = data.upcoming_corner_severity
        current_speed = data.current_speed_kmh

        # Base drift angle based on corner severity
        # Sharp corners need more angle, gentle corners need less
        base_angle = corner_severity * 45.0  # Max 45 degrees for sharpest corners

        # Adjust based on speed - higher speed needs more angle
        speed_factor = min(1.0, current_speed / 80.0)
        drift_angle = base_angle * (0.5 + speed_factor * 0.5)

        # Skill modifier - better drivers can handle more aggressive drifts
        skill_modifier = 0.5 + (self.params.drift_skill * 0.5)
        drift_angle *= skill_modifier

        # Target speed during drift (slower than normal racing)
        target_speed_factor = 0.7 - (corner_severity * 0.2)

        return drift_angle, target_speed_factor

    def _handle_racing(self, data: PerceptionData, cmd: DriveCommand, dt: float):
        """
        Enhanced racing logic with drift support and adaptive speed control.
        """
        # 0. Safety Check
        if not data.racing_line_target:
            cmd.drive_output = -1.0
            return

        # 1. Check if we should drift
        if self._should_drift(data):
            cmd.drift_mode = True
            drift_angle, speed_factor = self._calculate_drift_parameters(data)
            cmd.drift_angle = drift_angle
            # Modify target speed for drift entry
            data.target_speed_factor *= speed_factor

        # 1. Steering Control (Seek Behavior)
        target_vec = data.racing_line_target - data.position
        if target_vec.length_squared() > 0:
            target_dir = target_vec.normalize()
            
            # Add random error based on difficulty (Scaled by dt)
            if random.random() < self.params.error_chance * dt:
                target_dir = target_dir.rotate(random.uniform(-15, 15))
                
            angle_diff = data.heading.angle_to(target_dir)
            
            # Deadzone: Ignore small errors to prevent jitter
            if abs(angle_diff) < 3.0:
                cmd.steering = 0.0
            else:
                # Proportional steering with clamping
                # 45 degrees error = full steering
                cmd.steering = max(-1.0, min(1.0, angle_diff / 45.0))
        
        # 2. Advanced Speed Control with Adaptive Intelligence
        current_speed = data.current_speed_kmh
        target_speed = self._calculate_adaptive_target_speed(data, cmd)

        # Apply speed adaptation intelligence
        adaptation_factor = self.params.speed_adaptation
        if adaptation_factor > 0.5:
            # Intelligent drivers adjust speed more precisely
            target_speed = self._apply_intelligent_speed_adjustments(data, target_speed)

        # Apply braking margin (safety buffer)
        target_speed *= self.params.braking_margin
        
        # Throttle/Brake Logic
        if current_speed < target_speed - 5.0:
            # Accelerate
            cmd.drive_output = 1.0
        elif current_speed > target_speed + 5.0:
            # Brake
            # Proportional braking: Harder braking if way too fast
            overshoot = current_speed - target_speed
            brake_force = min(1.0, overshoot / 20.0) # Full brake if 20km/h over limit
            cmd.drive_output = -brake_force
        else:
            # Coast
            cmd.drive_output = 0.0
            
        # Minimum Momentum (Anti-Stop Logic)
        # ADJUSTED: Lower threshold and only override COAST, not BRAKE
        # Changed from 40.0 to 20.0 km/h to work with slower acceleration
        MIN_ROLLING_SPEED = 20.0
        if current_speed < MIN_ROLLING_SPEED and cmd.drive_output == 0.0:
            # Only force throttle if we're COASTING, not if we're braking!
            cmd.drive_output = 0.5  # Gentle acceleration to maintain momentum
            
        # Boost Logic (Straight line only)
        # ADJUSTED: Only boost if we're already accelerating, not if braking
        if steering_factor < 0.1 and current_speed < target_speed - 20.0 and cmd.drive_output > 0:
            cmd.drive_output = 1.0

    def _calculate_adaptive_target_speed(self, data: PerceptionData, cmd: DriveCommand) -> float:
        """
        Calculate target speed based on track conditions, corners, and AI intelligence.
        """
        # Base speed parameters
        MAX_SPEED_KMH = 220.0 * self.params.speed_factor
        MIN_SPEED_KMH = 50.0  # Minimum cornering speed

        # Current steering and curvature factors
        steering_factor = abs(cmd.steering)
        curvature_factor = data.next_curvature * 2.0

        # Enhanced corner analysis
        corner_speed_reduction = self._calculate_corner_speed_reduction(data)

        # Combine factors - use the most restrictive
        slowdown_factor = max(steering_factor, curvature_factor, corner_speed_reduction)

        # Calculate base target speed
        target_speed = MAX_SPEED_KMH - (slowdown_factor * (MAX_SPEED_KMH - MIN_SPEED_KMH))

        return target_speed

    def _calculate_corner_speed_reduction(self, data: PerceptionData) -> float:
        """
        Calculate speed reduction needed for upcoming corners.
        """
        if data.corner_entry_distance == float('inf'):
            return 0.0

        corner_severity = data.upcoming_corner_severity
        distance_to_corner = data.corner_entry_distance

        # Speed reduction based on corner severity and distance
        if distance_to_corner < 100:
            # Very close to corner - significant reduction
            return corner_severity * 0.8
        elif distance_to_corner < 200:
            # Approaching corner - moderate reduction
            return corner_severity * 0.5
        elif distance_to_corner < 300:
            # Corner ahead - light reduction
            return corner_severity * 0.2
        else:
            return 0.0

    def _apply_intelligent_speed_adjustments(self, data: PerceptionData, base_target_speed: float) -> float:
        """
        Apply intelligent speed adjustments based on track conditions and situation.
        """
        adjusted_speed = base_target_speed

        # 1. Straight line speed boost
        if data.upcoming_corner_severity < 0.2 and data.corner_entry_distance > 300:
            # Long straight - increase speed
            straight_boost = 1.0 + (self.params.speed_adaptation * 0.2)
            adjusted_speed *= straight_boost

        # 2. Corner entry speed optimization
        elif data.corner_entry_distance < 150 and data.upcoming_corner_severity > 0.4:
            # Approaching corner - optimize entry speed
            if self.params.drift_skill > 0.5 and data.upcoming_corner_severity > 0.6:
                # Skilled driver can take corners faster
                corner_skill_bonus = 1.0 + (self.params.drift_skill * 0.15)
                adjusted_speed *= corner_skill_bonus
            else:
                # Conservative approach for less skilled drivers
                adjusted_speed *= 0.9

        # 3. Traffic-aware speed adjustment
        if data.opponent_in_front and data.nearest_opponent_dist < 200:
            # Reduce speed when following closely
            following_distance_factor = data.nearest_opponent_dist / 200.0
            adjusted_speed *= (0.7 + following_distance_factor * 0.3)

        # 4. Track surface adaptation (if available)
        if not data.on_track:
            # Off-track - significant speed reduction
            adjusted_speed *= 0.6

        return adjusted_speed

    def _handle_overtaking(self, data: PerceptionData, cmd: DriveCommand, dt: float):
        """
        Intelligent overtaking with trajectory planning and risk assessment.
        """
        # Start with normal racing behavior
        self._handle_racing(data, cmd, dt)

        # Analyze overtaking opportunity
        overtake_side, confidence = self._analyze_overtaking_opportunity(data)

        if confidence > 0.3:  # Minimum confidence threshold
            # Calculate overtaking trajectory
            overtake_offset = self._calculate_overtaking_trajectory(data, overtake_side)

            # Apply intelligence-based modifications
            intelligence_factor = self.params.overtaking_intelligence

            # Steering adjustment for overtaking
            base_steering_offset = overtake_side * 0.4 * confidence
            intelligent_offset = base_steering_offset * intelligence_factor

            cmd.steering += intelligent_offset
            cmd.steering = max(-1.0, min(1.0, cmd.steering))

            # Throttle management during overtaking
            if self._is_overtaking_safe(data, overtake_side):
                # Full throttle if safe
                cmd.drive_output = min(1.0, cmd.drive_output + 0.2)
            else:
                # Conservative approach if risky
                cmd.drive_output *= 0.9
        else:
            # Stay behind if no good opportunity
            cmd.drive_output *= 0.8  # Reduce speed to maintain safe distance

    def _analyze_overtaking_opportunity(self, data: PerceptionData) -> Tuple[float, float]:
        """
        Analyze the best side and confidence level for overtaking.
        Returns: (side: -1.0 for left, 1.0 for right, confidence: 0.0-1.0)
        """
        if data.nearest_opponent_dist > 300:  # Too far to consider overtaking
            return 0.0, 0.0

        # Check track width and available space
        left_space = self._estimate_available_space(data, -1.0)
        right_space = self._estimate_available_space(data, 1.0)

        # Prefer the side with more space
        if left_space > right_space and left_space > 100:
            side = -1.0
            confidence = min(1.0, left_space / 150.0)
        elif right_space > left_space and right_space > 100:
            side = 1.0
            confidence = min(1.0, right_space / 150.0)
        else:
            return 0.0, 0.0  # Not enough space on either side

        # Reduce confidence based on opponent's predicted movement
        if data.predicted_opponent_pos:
            predicted_blocking = self._will_opponent_block(data, side)
            if predicted_blocking:
                confidence *= 0.5

        # Increase confidence if we're significantly faster
        speed_advantage = data.current_speed_kmh - (data.opponent_velocity.length() * 15.0)
        if speed_advantage > 10:  # 10 km/h advantage
            confidence *= 1.2

        return side, min(1.0, confidence)

    def _estimate_available_space(self, data: PerceptionData, side: float) -> float:
        """
        Estimate available space on the specified side (-1.0 = left, 1.0 = right).
        """
        # Use lidar data to estimate space
        if side < 0:  # Left side
            if len(data.lidar_distances) > 3:
                return data.lidar_distances[3] * 200  # Convert to pixels
        else:  # Right side
            if len(data.lidar_distances) > 4:
                return data.lidar_distances[4] * 200  # Convert to pixels

        return data.track_width * 0.4  # Fallback estimate

    def _calculate_overtaking_trajectory(self, data: PerceptionData, side: float) -> float:
        """
        Calculate the optimal trajectory offset for overtaking.
        """
        # Base offset based on opponent position and side
        base_offset = side * 0.3

        # Adjust based on opponent's angle and distance
        if data.nearest_opponent_dist < 150:
            # Close opponent - more aggressive offset
            base_offset *= 1.5
        elif data.nearest_opponent_dist > 250:
            # Distant opponent - gentler offset
            base_offset *= 0.7

        return base_offset

    def _is_overtaking_safe(self, data: PerceptionData, side: float) -> bool:
        """
        Determine if overtaking on the specified side is safe.
        """
        # Check for upcoming corners
        if data.upcoming_corner_severity > 0.5 and data.corner_entry_distance < 200:
            return False  # Don't overtake before sharp corners

        # Check available space
        available_space = self._estimate_available_space(data, side)
        if available_space < 120:  # Need minimum space
            return False

        # Check if opponent is likely to move into our path
        if data.predicted_opponent_pos and self._will_opponent_block(data, side):
            return False

        return True

    def _will_opponent_block(self, data: PerceptionData, side: float) -> bool:
        """
        Predict if opponent will move to block our overtaking attempt.
        """
        if not data.predicted_opponent_pos:
            return False

        # Simple prediction: if opponent is moving towards our intended side
        opponent_lateral_movement = data.opponent_velocity.x if side > 0 else -data.opponent_velocity.x

        # If opponent is moving towards our overtaking side with significant speed
        return opponent_lateral_movement > 2.0

    def _handle_defending(self, data: PerceptionData, cmd: DriveCommand, dt: float):
        """
        Intelligent defensive driving to block player overtaking attempts.
        """
        # Start with normal racing behavior
        self._handle_racing(data, cmd, dt)

        # Only defend if opponent is close behind
        if not data.opponent_behind or data.nearest_opponent_dist > 200:
            return

        # Analyze player's overtaking intention
        player_intention = self._analyze_player_intention(data)

        # Calculate defensive positioning
        defensive_position = self._calculate_defensive_position(data, player_intention)

        # Apply blocking based on aggressiveness level
        aggressiveness = self.params.blocking_aggressiveness

        if aggressiveness > 0.1:
            # Apply defensive steering
            defensive_steering = defensive_position * aggressiveness * 0.4
            cmd.steering += defensive_steering
            cmd.steering = max(-1.0, min(1.0, cmd.steering))

            # Adjust speed for defensive driving
            if aggressiveness > 0.5:
                # More aggressive - maintain speed to block
                cmd.drive_output = max(cmd.drive_output, 0.8)
            else:
                # Less aggressive - slight speed reduction for safety
                cmd.drive_output *= 0.95

    def _analyze_player_intention(self, data: PerceptionData) -> float:
        """
        Analyze which side the player is likely to attempt overtaking.
        Returns: -1.0 for left, 1.0 for right, 0.0 for no clear intention
        """
        if not data.predicted_opponent_pos:
            # Fallback to current angle
            return 1.0 if data.nearest_opponent_angle > 0 else -1.0

        # Compare predicted position with current position
        current_lateral = data.nearest_opponent_angle

        # Predict lateral movement
        if data.opponent_velocity.length() > 0:
            # Calculate lateral component of opponent velocity
            lateral_velocity = data.opponent_velocity.x

            if abs(lateral_velocity) > 1.0:  # Significant lateral movement
                return 1.0 if lateral_velocity > 0 else -1.0

        # Fallback to current angle
        return 1.0 if current_lateral > 0 else -1.0

    def _calculate_defensive_position(self, data: PerceptionData, player_intention: float) -> float:
        """
        Calculate optimal defensive position to block player's intended path.
        """
        # Base defensive position - move towards player's intended side
        base_position = player_intention * 0.5

        # Adjust based on track position and upcoming corners
        if data.upcoming_corner_severity > 0.4 and data.corner_entry_distance < 300:
            # Before corners, position to control the racing line
            if data.upcoming_corner_severity > 0.7:
                # Sharp corner - take inside line
                corner_direction = 1.0 if data.next_curvature > 0 else -1.0
                base_position = corner_direction * 0.3
            else:
                # Gentle corner - less aggressive positioning
                base_position *= 0.7

        # Limit defensive movement based on track boundaries
        available_space_left = self._estimate_available_space(data, -1.0)
        available_space_right = self._estimate_available_space(data, 1.0)

        if base_position < 0 and available_space_left < 100:
            base_position *= 0.5  # Reduce left movement if space is limited
        elif base_position > 0 and available_space_right < 100:
            base_position *= 0.5  # Reduce right movement if space is limited

        return base_position

    def _handle_recovery(self, data: PerceptionData, cmd: DriveCommand, dt: float):
        """
        Advanced Recovery Logic:
        - If stuck against a wall (speed ~0), reverse.
        - If facing away from the track, reverse and turn.
        - If facing the track, crawl forward.
        """
        if not data.racing_line_target:
            return

        target_vec = data.racing_line_target - data.position
        angle_to_target = data.heading.angle_to(target_vec)
        
        # Check if we are stuck (low speed despite throttle)
        is_stuck = data.current_speed_kmh < 5.0
        
        # Check if we are facing the wrong way (more than 90 degrees off target)
        wrong_way = abs(angle_to_target) > 90
        
        if is_stuck or wrong_way:
            # Reverse logic
            cmd.drive_output = -1.0
            # Steer opposite to target to swing front around
            if angle_to_target > 0:
                cmd.steering = -1.0
            else:
                cmd.steering = 1.0
        else:
            # Crawl forward towards target
            cmd.drive_output = 0.5
            cmd.steering = max(-1.0, min(1.0, angle_to_target / 30.0))
            
        # Reset filters for instant reaction
        self.steer_filter.reset(cmd.steering)
        self.drive_filter.reset(cmd.drive_output)

class ExecutionModule:
    """
    Translates abstract commands into concrete car inputs with smoothing.
    """
    def __init__(self, car: Car):
        self.car = car
        # Reduced smoothing factor significantly because car.py physics
        # already has high turn speed. We need slow input changes.
        # Increased to 0.15 as per "Control Inertia" requirement
        self.steer_smoothing = 0.15

    def execute(self, cmd: DriveCommand, dt: float):
        # Handle drift mode
        if cmd.drift_mode:
            self._execute_drift(cmd, dt)
        else:
            self._execute_normal_driving(cmd, dt)

    def _execute_normal_driving(self, cmd: DriveCommand, dt: float):
        """Normal driving execution with smooth steering."""
        # Smooth steering
        current_steer = self.car.steering
        diff = cmd.steering - current_steer

        # Frame-rate independent smoothing
        change = diff * self.steer_smoothing * (dt * 60)
        self.car.steering = max(-1.0, min(1.0, current_steer + change))

        # Map drive_output to Throttle/Brake
        # drive_output is already smoothed by DecisionModule
        if cmd.drive_output > 0:
            self.car.throttle = cmd.drive_output
            self.car.is_braking = 0.0
        else:
            self.car.throttle = 0.0
            # CLAMP AI BRAKING (Fix AI Panic)
            # Limit braking to 50% to prevent slamming, unless collision logic overrides later
            brake_val = -cmd.drive_output
            self.car.is_braking = min(brake_val, 0.5)

    def _execute_drift(self, cmd: DriveCommand, dt: float):
        """
        Execute drift maneuver with more aggressive steering and throttle control.
        """
        # More aggressive steering for drift initiation
        target_steering = cmd.steering

        # Add drift angle bias
        if cmd.drift_angle > 0:
            # Determine drift direction based on corner
            drift_direction = 1.0 if cmd.steering > 0 else -1.0
            drift_steering_bias = (cmd.drift_angle / 45.0) * drift_direction * 0.3
            target_steering += drift_steering_bias

        # Apply steering with less smoothing for more responsive drift
        current_steer = self.car.steering
        diff = target_steering - current_steer

        # Faster steering response for drifts
        drift_smoothing = self.steer_smoothing * 2.0  # 2x faster response
        change = diff * drift_smoothing * (dt * 60)
        self.car.steering = max(-1.0, min(1.0, current_steer + change))

        # Drift throttle control - maintain power through the drift
        if cmd.drive_output > 0:
            # Boost throttle slightly during drift to maintain momentum
            drift_throttle = min(1.0, cmd.drive_output * 1.1)
            self.car.throttle = drift_throttle
            self.car.is_braking = 0.0
        else:
            # Light braking during drift entry
            self.car.throttle = 0.0
            brake_val = -cmd.drive_output * 0.7  # Gentler braking during drift
            self.car.is_braking = min(brake_val, 0.4)

# --- Main Driver Class ---

class AIDriver:
    """
    Main AI Controller. Orchestrates Perception, Decision, and Execution.
    """
    def __init__(self, car: Car, track_manager, nav_manager, difficulty_str: str = "NORMAL", is_circuit: bool = True, tmx_map=None):
        self.car = car
        self.track_manager = track_manager
        self.tmx_map = tmx_map
        self.is_circuit = is_circuit
        
        # Difficulty mapping
        diff_map = {
            "EASY": DifficultyLevel.BEGINNER,
            "NORMAL": DifficultyLevel.AMATEUR,
            "HARD": DifficultyLevel.PRO,
            "ELITE": DifficultyLevel.ELITE
        }
        level = diff_map.get(difficulty_str.upper(), DifficultyLevel.AMATEUR)
        
        # Initialize Modules
        self.decision = DecisionModule(level)
        self.perception = PerceptionModule(car, track_manager, tmx_map, is_circuit)
        self.execution = ExecutionModule(car)
        self.prediction = PredictionModule(self.decision.params)
        
        # Apply difficulty params
        self.perception.lookahead_mult = self.decision.params.lookahead_mult
        self.car.ai_grip_multiplier = self.decision.params.grip_bonus
        
        # Legacy/Compatibility flags
        self.reached_final_waypoint = False
        self.target_wp_index = 0
        self.active = True
        self.stuck_timer = 0.0
        self.reverse_timer = 0.0
        self.scan_timer = 0

    def reset(self):
        """Resets the AI's internal state for a race restart."""
        self.target_wp_index = 0
        self.stuck_timer = 0.0
        self.reverse_timer = 0.0
        self.reached_final_waypoint = False
        
        # Reset the low-pass filters in the decision module to prevent
        # carrying over old steering/throttle values.
        if hasattr(self.decision, 'steer_filter'):
            self.decision.steer_filter.reset()
        if hasattr(self.decision, 'drive_filter'):
            self.decision.drive_filter.reset()

    def update(self, dt: float, player_car: Optional[Car] = None):
        if not self.active or self.car.finished:
            self.car.throttle = 0
            self.car.is_braking = 1.0
            return

        # 0. Periodic nearest waypoint scan (Fail-safe)
        self.scan_timer += 1
        if self.scan_timer >= 60 or self.car.velocity.length() < 2.0:
            self.scan_timer = 0
            self._find_nearest_waypoint_ahead()

        # 1. Perception
        opponents = [player_car] if player_car else []
        perception_data = self.perception.scan(opponents, self.target_wp_index)

        # 1.5. Strategic Planning
        self.prediction.update_strategic_plan(perception_data, dt)
        strategic_recommendations = self.prediction.get_strategic_recommendation(perception_data)
        
        # DEBUG OUTPUT (every 60 frames = ~1 second)
        if not hasattr(self, 'ai_debug_timer'):
            self.ai_debug_timer = 0
        self.ai_debug_timer += 1
        
        if self.ai_debug_timer % 60 == 0:
            if perception_data.racing_line_target is None:
                print(f"[AI DEBUG] ❌ NO RACING LINE TARGET!")
                print(f"  Using waypoints: {self.perception.using_waypoints}")
                if self.perception.racing_line:
                    print(f"  Racing line points: {len(self.perception.racing_line.points)}")
            else:
                dist = self.car.position.distance_to(perception_data.racing_line_target)
                angle_to_target = self.car.position.angle_to(perception_data.racing_line_target)
                print(f"[AI DEBUG] ✅ Racing Line Active")
                print(f"  Target dist: {dist:.1f}px, angle: {angle_to_target:.1f}°")
                print(f"  Car speed: {self.car.velocity.length():.2f} units")
                print(f"  Car position: ({self.car.x:.0f}, {self.car.y:.0f})")
                print(f"  Target position: ({perception_data.racing_line_target.x:.0f}, {perception_data.racing_line_target.y:.0f})")
        
        # Sync index if using waypoints to prevent lag
        if hasattr(perception_data, 'matched_waypoint_index'):
             # Only update if we moved forward (handle wrapping logic carefully)
             # For simplicity, we trust the projection but ensure we don't skip the finish line logic
             # The finish line logic is in _advance_waypoint, so we let _update_waypoint_progress handle the actual increment
             # BUT we update the hint for the next frame's search
             self.target_wp_index = perception_data.matched_waypoint_index

        # 2. Decision (Enhanced with Strategic Input)
        command = self.decision.decide(perception_data, dt)

        # Apply strategic recommendations
        if strategic_recommendations:
            command = self._apply_strategic_recommendations(command, strategic_recommendations, perception_data)
        
        # DEBUG: Show what command was generated
        if self.ai_debug_timer % 60 == 0:
            print(f"  Decision state: {self.decision.state}")
            print(f"  Command → Steering: {command.steering:.2f}, Drive: {command.drive_output:.2f}")
            print(f"  Executed → Throttle: {self.car.throttle:.2f}, Brake: {self.car.is_braking:.2f}")
        
        # 3. Progress Tracking (Dot Product Check)
        self._update_waypoint_progress()

        # 4. Anti-Stuck Mechanism (Recovery)
        if self.reverse_timer > 0:
            self.reverse_timer -= dt
            command.drive_output = -1.0 # Brake/Reverse
            command.steering = 1.0 # Turn while backing up
        else:
            # ADJUSTED: Lower threshold for slower acceleration
            # Changed from 5.0 to 2.0 to account for accel_multiplier
            if self.car.velocity.length() < 2.0 and command.drive_output > 0:
                self.stuck_timer += dt
                # ADJUSTED: Longer wait time (4s instead of 2s) before declaring stuck
                if self.stuck_timer > 4.0:
                    print(f"[AI DEBUG] STUCK! Initiating reverse maneuver.")
                    self.reverse_timer = 1.5
                    self.stuck_timer = 0.0
            else:
                self.stuck_timer = 0.0

        # 5. Execution
        self.execution.execute(command, dt)

    def _apply_strategic_recommendations(self, command: DriveCommand, recommendations: Dict[str, Any], data: PerceptionData) -> DriveCommand:
        """
        Apply strategic recommendations to modify the basic driving command.
        """
        # Apply speed factor recommendations
        if 'recommended_speed_factor' in recommendations:
            speed_factor = recommendations['recommended_speed_factor']
            if speed_factor != 1.0:
                command.drive_output *= speed_factor
                command.drive_output = max(-1.0, min(1.0, command.drive_output))

        # Apply positioning recommendations
        if 'recommended_positioning' in recommendations:
            positioning = recommendations['recommended_positioning']
            if abs(positioning) > 0.1:
                command.steering += positioning * 0.3  # Moderate influence
                command.steering = max(-1.0, min(1.0, command.steering))

        # Apply safety priority
        if recommendations.get('should_prioritize_safety', False):
            # More conservative driving
            command.drive_output *= 0.8
            if abs(command.steering) > 0.7:
                command.steering *= 0.8  # Reduce aggressive steering

        # Apply aggressive recommendations
        if recommendations.get('should_be_aggressive', False):
            # More aggressive driving for overtaking
            if command.drive_output > 0:
                command.drive_output = min(1.0, command.drive_output * 1.1)

        # Apply defensive recommendations
        if recommendations.get('should_be_defensive', False):
            # Focus on blocking rather than speed
            if data.opponent_behind and data.nearest_opponent_dist < 150:
                # Reduce speed slightly to control the race
                command.drive_output *= 0.95

        return command

    def _update_waypoint_progress(self):
        """Dynamic waypoint selection using dynamic radius and dot product."""
        if not self.track_manager.waypoints:
            return

        target_wp = self.track_manager.waypoints[self.target_wp_index]
        to_wp = target_wp - self.car.position
        distance = to_wp.length()
        heading = pg.Vector2(1, 0).rotate(self.car.angle)

        # Dynamic Acceptance Radius based on Speed
        # At low speed (0), radius is 200px.
        # At high speed (200 km/h approx 13.3 units), radius increases.
        # 13.3 * 15 = 200px extra -> 400px total.
        # This ensures we don't "miss" the waypoint when flying past it.
        current_speed = self.car.velocity.length()
        # Reduced radius for dense waypoints (approx 40px spacing)
        # Base 60px allows for some error, plus speed factor
        acceptance_radius = 60.0 + (current_speed * 5.0)

        # 1. Classic reach (within radius)
        if distance < acceptance_radius:
            self._advance_waypoint()
            return

        # STRICT PATH FOLLOWING: Removed "Missed waypoint check"
        # AI must physically reach the waypoint radius to advance.
        # This prevents cutting corners or skipping sections.

    def _advance_waypoint(self):
        """Increments waypoint index and handles finish."""
        self.target_wp_index = (self.target_wp_index + 1) % len(self.track_manager.waypoints)
        if not self.is_circuit and self.target_wp_index == 0:
            self.reached_final_waypoint = True
            self.car.finished = True

    def _find_nearest_waypoint_ahead(self):
        """Scans for the closest waypoint that is in front of the car."""
        if not self.track_manager.waypoints:
            return

        best_idx = self.target_wp_index
        min_dist = float('inf')
        heading = pg.Vector2(1, 0).rotate(self.car.angle)

        # STRICT PATH FOLLOWING: Only look for the current or next waypoint
        # Do not scan the entire track to prevent jumping ahead.
        
        candidates = [self.target_wp_index]
        next_idx = (self.target_wp_index + 1) % len(self.track_manager.waypoints)
        candidates.append(next_idx)
        
        for i in candidates:
            wp = self.track_manager.waypoints[i]
            to_wp = wp - self.car.position
            dist = to_wp.length()
            
            if dist < min_dist:
                min_dist = dist
                best_idx = i
        
        # Only update if we found a better candidate that is strictly the next one
        if best_idx == next_idx:
             # Only advance if we are very close to the current one (fail-safe)
             # or if we are closer to next than current AND current is behind us
             current_wp = self.track_manager.waypoints[self.target_wp_index]
             dist_current = self.car.position.distance_to(current_wp)
             
             if dist_current < 100: # Close enough to skip if we found next
                 self.target_wp_index = best_idx

    def draw_debug(self, surface, camera):
        """Draws debug info: Simplified Path, Rabbit, and Telemetry."""
        
        # 1. The Simplified Path (Yellow Line)
        if hasattr(self.perception, 'racing_line') and self.perception.racing_line:
             points = [camera.apply_pos(p) for p in self.perception.racing_line.points]
             if len(points) > 1:
                 # Draw Yellow Line for simplified path
                 pg.draw.lines(surface, (255, 255, 0), self.perception.racing_line.is_circuit, points, 2)

        # 2. The Rabbit (Red Circle + Line)
        # We re-scan to get the current target visualization
        perception_data = self.perception.scan([], self.target_wp_index)
        
        if perception_data.racing_line_target:
            t_pos = camera.apply_pos(perception_data.racing_line_target)
            c_pos = camera.apply_pos(self.car.position)
            
            # Red Line from Car to Target
            pg.draw.line(surface, (255, 0, 0), c_pos, t_pos, 2)
            # Red Circle at Target
            pg.draw.circle(surface, (255, 0, 0), t_pos, 8)

        # 3. Input Telemetry (UI Bars)
        # Draw in bottom-right corner
        screen_w, screen_h = surface.get_size()
        ui_x = screen_w - 150
        ui_y = screen_h - 150
        bar_w = 20
        bar_h = 100
        
        # Throttle (Green)
        t_height = int(self.car.throttle * bar_h)
        pg.draw.rect(surface, (50, 50, 50), (ui_x, ui_y, bar_w, bar_h)) # BG
        pg.draw.rect(surface, (0, 255, 0), (ui_x, ui_y + (bar_h - t_height), bar_w, t_height))
        
        # Brake (Red)
        b_height = int(self.car.is_braking * bar_h)
        pg.draw.rect(surface, (50, 50, 50), (ui_x + 30, ui_y, bar_w, bar_h)) # BG
        pg.draw.rect(surface, (255, 0, 0), (ui_x + 30, ui_y + (bar_h - b_height), bar_w, b_height))
        
        # Steering (Blue) - Horizontal Bar
        # Center is 0. Left is -1, Right is 1.
        s_width = 100
        s_center_x = ui_x + 60 + (s_width // 2)
        s_y = ui_y + bar_h // 2
        
        pg.draw.rect(surface, (50, 50, 50), (ui_x + 60, s_y - 10, s_width, 20)) # BG
        
        steer_val = self.car.steering # -1 to 1
        bar_len = int(steer_val * (s_width / 2))
        
        if bar_len > 0:
            pg.draw.rect(surface, (0, 100, 255), (s_center_x, s_y - 8, bar_len, 16))
        else:
            pg.draw.rect(surface, (0, 100, 255), (s_center_x + bar_len, s_y - 8, -bar_len, 16))

        # 4. Speedometer Text
        # Assuming a default font is available or we can use a system font
        font = pg.font.SysFont('Arial', 24)
        speed_text = f"SPEED: {self.car.velocity.length():.1f} / {self.car.max_speed_units:.1f}"
        text_surf = font.render(speed_text, True, (255, 255, 255))
        surface.blit(text_surf, (ui_x, ui_y - 30))

        # 5. Physics Vectors (Blue = Velocity, Red = Friction)
        c_pos = camera.apply_pos(self.car.position)
        
        # Velocity (Blue) - Scaled for visibility
        vel_end = c_pos + self.car.velocity * 5.0
        pg.draw.line(surface, (0, 0, 255), c_pos, vel_end, 3)
        
        # Friction/Resistance (Red) - Approximate direction (opposite to velocity)
        if self.car.velocity.length() > 0:
            friction_dir = -self.car.velocity.normalize()
            # Magnitude based on friction coeff (visual approximation)
            friction_mag = (1.0 - self.car.friction) * 500.0
            fric_end = c_pos + friction_dir * friction_mag
            pg.draw.line(surface, (255, 0, 0), c_pos, fric_end, 2)